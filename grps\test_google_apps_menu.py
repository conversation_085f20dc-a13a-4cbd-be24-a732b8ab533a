#!/usr/bin/env python3
"""
Test script for Google Apps Menu detection and interaction.
This script tests the various fallback strategies for locating the Google Apps menu.
"""

import sys
import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException, TimeoutException

# Add the PyFiles directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'PyFiles'))

def setup_test_browser():
    """Setup a test browser with basic options"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    driver = webdriver.Chrome(options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def test_google_apps_menu_selectors(driver):
    """Test various Google Apps menu selectors"""
    print("Testing Google Apps Menu Selectors...")
    print("=" * 50)
    
    # Navigate to Google
    driver.get("https://www.google.com")
    time.sleep(3)
    
    # List of selectors to test
    selectors = [
        # Current/recent selectors
        ('XPath', '//a[@aria-label="Google apps"]'),
        ('XPath', '//a[@aria-label="Google Apps"]'),
        ('XPath', '//a[@title="Google apps"]'),
        ('XPath', '//a[@title="Google Apps"]'),
        
        # Alternative aria-label variations
        ('XPath', '//a[contains(@aria-label, "Google apps")]'),
        ('XPath', '//a[contains(@aria-label, "apps")]'),
        ('XPath', '//a[contains(@title, "Google apps")]'),
        ('XPath', '//a[contains(@title, "apps")]'),
        
        # Button variations
        ('XPath', '//button[@aria-label="Google apps"]'),
        ('XPath', '//button[@title="Google apps"]'),
        ('XPath', '//button[contains(@aria-label, "Google apps")]'),
        ('XPath', '//button[contains(@title, "Google apps")]'),
        
        # Class-based selectors
        ('XPath', '//a[contains(@class, "gb_d")]'),
        ('XPath', '//a[contains(@class, "gb_A")]'),
        ('XPath', '//div[contains(@class, "gb_d")]'),
        ('XPath', '//div[contains(@class, "gb_A")]'),
        
        # SVG-based selectors
        ('XPath', '//a[.//svg]'),
        ('XPath', '//button[.//svg]'),
        ('XPath', '//a[contains(@class, "gb_") and .//svg]'),
        ('XPath', '//button[contains(@class, "gb_") and .//svg]'),
        
        # Data attribute selectors
        ('XPath', '//a[@data-pid]'),
        ('XPath', '//button[@data-pid]'),
        
        # Generic fallbacks
        ('XPath', '//div[@id="gbwa"]//a'),
        ('XPath', '//div[contains(@class, "gb_")]//a[position()=last()]'),
        ('XPath', '//header//a[.//svg]'),
        ('XPath', '//nav//a[.//svg]')
    ]
    
    successful_selectors = []
    
    for i, (selector_type, selector) in enumerate(selectors, 1):
        try:
            print(f"Testing selector {i:2d}: {selector}")
            
            if selector_type == 'XPath':
                element = driver.find_element(By.XPATH, selector)
            else:
                element = driver.find_element(By.CSS_SELECTOR, selector)
            
            if element and element.is_displayed():
                print(f"  ✅ SUCCESS: Found element")
                print(f"     Tag: {element.tag_name}")
                print(f"     Text: {element.text[:50]}...")
                print(f"     Aria-label: {element.get_attribute('aria-label')}")
                print(f"     Title: {element.get_attribute('title')}")
                print(f"     Class: {element.get_attribute('class')}")
                successful_selectors.append((i, selector_type, selector))
            else:
                print(f"  ❌ FOUND but not displayed")
                
        except NoSuchElementException:
            print(f"  ❌ NOT FOUND")
        except Exception as e:
            print(f"  ❌ ERROR: {str(e)}")
        
        print()
    
    return successful_selectors

def test_javascript_detection(driver):
    """Test JavaScript-based detection"""
    print("Testing JavaScript Detection...")
    print("=" * 50)
    
    js_script = """
    // Function to find Google Apps menu using various strategies
    function findGoogleAppsMenu() {
        // Strategy 1: Look for elements with Google apps related attributes
        const candidates = [
            ...document.querySelectorAll('a[aria-label*="apps" i]'),
            ...document.querySelectorAll('a[title*="apps" i]'),
            ...document.querySelectorAll('button[aria-label*="apps" i]'),
            ...document.querySelectorAll('button[title*="apps" i]'),
            ...document.querySelectorAll('a[aria-label*="Google" i]'),
            ...document.querySelectorAll('button[aria-label*="Google" i]')
        ];
        
        // Strategy 2: Look for elements in the top-right area with SVG icons
        const topRightElements = document.querySelectorAll('header a, nav a, div[class*="gb_"] a, div[class*="gb_"] button');
        for (let elem of topRightElements) {
            if (elem.querySelector('svg') && elem.getBoundingClientRect().right > window.innerWidth * 0.7) {
                candidates.push(elem);
            }
        }
        
        // Strategy 3: Look for elements with specific Google classes
        const googleClassElements = [
            ...document.querySelectorAll('a[class*="gb_"]'),
            ...document.querySelectorAll('button[class*="gb_"]'),
            ...document.querySelectorAll('div[class*="gb_"] a'),
            ...document.querySelectorAll('div[class*="gb_"] button')
        ];
        candidates.push(...googleClassElements);
        
        // Filter and validate candidates
        const validCandidates = [];
        for (let candidate of candidates) {
            if (candidate && 
                candidate.offsetWidth > 0 && 
                candidate.offsetHeight > 0 && 
                !candidate.hidden &&
                candidate.style.display !== 'none') {
                
                // Check if it's in the expected position (top-right area)
                const rect = candidate.getBoundingClientRect();
                if (rect.right > window.innerWidth * 0.6 && rect.top < window.innerHeight * 0.2) {
                    validCandidates.push({
                        element: candidate,
                        tagName: candidate.tagName,
                        className: candidate.className,
                        ariaLabel: candidate.getAttribute('aria-label'),
                        title: candidate.getAttribute('title'),
                        rect: {
                            left: rect.left,
                            top: rect.top,
                            right: rect.right,
                            bottom: rect.bottom,
                            width: rect.width,
                            height: rect.height
                        }
                    });
                }
            }
        }
        
        return validCandidates;
    }
    
    return findGoogleAppsMenu();
    """
    
    try:
        result = driver.execute_script(js_script)
        
        if result and len(result) > 0:
            print(f"✅ JavaScript detection found {len(result)} candidate(s):")
            for i, candidate in enumerate(result, 1):
                print(f"  Candidate {i}:")
                print(f"    Tag: {candidate['tagName']}")
                print(f"    Class: {candidate['className']}")
                print(f"    Aria-label: {candidate['ariaLabel']}")
                print(f"    Title: {candidate['title']}")
                print(f"    Position: ({candidate['rect']['left']:.1f}, {candidate['rect']['top']:.1f})")
                print(f"    Size: {candidate['rect']['width']:.1f} x {candidate['rect']['height']:.1f}")
                print()
            return result[0]['element']  # Return first candidate
        else:
            print("❌ JavaScript detection found no candidates")
            return None
            
    except Exception as e:
        print(f"❌ JavaScript detection failed: {str(e)}")
        return None

def main():
    """Main test function"""
    print("Google Apps Menu Detection Test")
    print("=" * 50)
    
    driver = None
    try:
        # Setup browser
        print("Setting up test browser...")
        driver = setup_test_browser()
        
        # Test selectors
        successful_selectors = test_google_apps_menu_selectors(driver)
        
        # Test JavaScript detection
        js_element = test_javascript_detection(driver)
        
        # Summary
        print("Test Summary")
        print("=" * 50)
        print(f"Successful selectors: {len(successful_selectors)}")
        for i, selector_type, selector in successful_selectors:
            print(f"  {i:2d}. {selector}")
        
        print(f"JavaScript detection: {'✅ SUCCESS' if js_element else '❌ FAILED'}")
        
        if successful_selectors or js_element:
            print("\n✅ At least one detection method works!")
        else:
            print("\n❌ No detection methods worked - Google may have changed their structure")
        
        # Keep browser open for manual inspection
        input("\nPress Enter to close browser...")
        
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
        
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    main()
