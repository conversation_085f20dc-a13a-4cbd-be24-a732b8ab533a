#!/usr/bin/env python3
"""
Script to discover correct 5sim country codes
"""

import requests
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def discover_countries():
    """Discover available countries from 5sim API"""
    try:
        # Load API key
        with open('grps/PyFiles/json/fivesim_config.json', 'r') as f:
            config = json.load(f)
        api_key = config.get('api_key', '')
        
        if not api_key:
            logger.error("No API key found")
            return
        
        session = requests.Session()
        session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json'
        })
        
        # Try different endpoints to discover countries
        endpoints = [
            "https://5sim.net/v1/guest/countries",
            "https://5sim.net/v1/guest/products",
            "https://5sim.net/v1/guest/prices"
        ]
        
        for endpoint in endpoints:
            logger.info(f"Trying endpoint: {endpoint}")
            try:
                response = session.get(endpoint, timeout=10)
                logger.info(f"Status: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Response data: {json.dumps(data, indent=2)}")
                    
                    # If it's a dict with country codes as keys
                    if isinstance(data, dict):
                        countries = list(data.keys())
                        logger.info(f"Found countries: {countries}")
                        
                        # Look for countries that might be US, UK, Canada, France, Germany
                        target_countries = {
                            'United States': ['usa', 'us', 'america', 'united_states'],
                            'United Kingdom': ['uk', 'gb', 'britain', 'england', 'united_kingdom'],
                            'Canada': ['ca', 'canada', 'can'],
                            'France': ['fr', 'france', 'fra'],
                            'Germany': ['de', 'germany', 'deutschland', 'ger']
                        }
                        
                        found_matches = {}
                        for country_name, possible_codes in target_countries.items():
                            for code in countries:
                                if code.lower() in possible_codes or any(pc in code.lower() for pc in possible_codes):
                                    found_matches[country_name] = code
                                    break
                        
                        logger.info(f"Potential matches: {found_matches}")
                        
                        # Test a few countries to see if they work
                        test_countries = countries[:10] if len(countries) > 10 else countries
                        logger.info(f"Testing first few countries: {test_countries}")
                        
                        for country in test_countries:
                            test_url = f"https://5sim.net/v1/guest/products/{country}/any"
                            try:
                                test_response = session.get(test_url, timeout=5)
                                if test_response.status_code == 200:
                                    test_data = test_response.json()
                                    if 'google' in test_data:
                                        google_info = test_data['google']
                                        qty = google_info.get('Qty', 0)
                                        price = google_info.get('Price', 0)
                                        logger.info(f"✓ {country}: {qty} Google numbers at ${price}")
                                    else:
                                        logger.info(f"- {country}: No Google service")
                                else:
                                    logger.info(f"✗ {country}: Status {test_response.status_code}")
                            except Exception as e:
                                logger.info(f"✗ {country}: Error - {str(e)}")
                        
                        return data
                else:
                    logger.error(f"Error response: {response.text}")
                    
            except Exception as e:
                logger.error(f"Error with {endpoint}: {str(e)}")
        
        # If no endpoints worked, try some common country codes
        logger.info("Trying common country codes...")
        common_codes = [
            'russia', 'china', 'india', 'brazil', 'indonesia', 
            'vietnam', 'philippines', 'bangladesh', 'nigeria',
            'any', '0', '1', '7', '44', '33', '49'  # numeric codes
        ]
        
        for code in common_codes:
            test_url = f"https://5sim.net/v1/guest/products/{code}/any"
            try:
                response = session.get(test_url, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if 'google' in data:
                        google_info = data['google']
                        qty = google_info.get('Qty', 0)
                        price = google_info.get('Price', 0)
                        logger.info(f"✓ WORKING: {code} - {qty} Google numbers at ${price}")
                elif response.status_code == 400:
                    logger.info(f"✗ {code}: bad country")
                else:
                    logger.info(f"? {code}: Status {response.status_code}")
            except Exception as e:
                logger.info(f"✗ {code}: Error - {str(e)}")
                
    except Exception as e:
        logger.error(f"Discovery failed: {str(e)}")

if __name__ == "__main__":
    discover_countries()
